import os
import re
import sys
import boto3
import json
import concurrent.futures
from tqdm import tqdm
import pandas as pd
import time
from collections import defaultdict

BASE_DIR = os.path.dirname(os.path.abspath('{}/../../'.format(__file__)))
sys.path.append(BASE_DIR)

from ude_analysis.src.build_prompt import main_prompt
from ude_analysis.src.constant import model_name_id_map


class LLM:
    def __init__(self, config=None, existing_claude_response=None, model_name="Claude 3.5 Sonnet", prompt_func=None):
        self.existing_claude_response = existing_claude_response
        self.config = config
        self.stop_flags = None
        self.current_pt_key = None
        self.model_name = model_name
        self.prompt_func = prompt_func if prompt_func else main_prompt
        self.all_clients = self._get_bedrock_clients()
        self.clients = {index: (model_id, client) for model_id in model_name_id_map[model_name] for index, client in enumerate(self.all_clients[model_id])}

    def _get_bedrock_clients(self):
        clients = defaultdict(list)
        compatible_regions = defaultdict(list)

        # read claude account information from dynamodb
        session = boto3.Session(profile_name='compatibility', region_name="us-east-1")
        dynamodb = session.resource('dynamodb')
        table = dynamodb.Table('claude_account')
        response = table.scan()
        
        for item in tqdm(response['Items'], desc="Getting bedrock clients"):
            # 测试Claude模型
            claude_body = json.dumps({
                "max_tokens": 1024,
                "system": f"",
                "messages": [{"role": "user", "content": [
                            {
                                "type": "text",
                                "text": "Hello"
                            }
                        ]}],
                "anthropic_version": "bedrock-2023-05-31"
            })

            test_client = boto3.client(service_name='bedrock-runtime', aws_access_key_id=item['access_key_id'],region_name=item['region_name'], aws_secret_access_key=item['secret_access_key'])
            
            for model_id in tqdm(model_name_id_map[self.model_name], desc="Testing model compatibility"):
                    try:
                        test_client.invoke_model(body=claude_body, modelId=model_id)
                        compatible_regions[model_id].append([item['access_key_id'], item['secret_access_key'], item['region_name']])
                        print(f"✅ 区域 {item['region_name']} 支持 {model_id} 模型")
                    except Exception as ex:
                        print(f"❌ {model_id}模型测试失败 ({item['region_name']}): {ex}")

        
        for model_id, regions in compatible_regions.items():
            max_clients = len(regions) * 3
            for i in range(max_clients):
                try:
                    client = boto3.client(service_name='bedrock-runtime',
                                                        aws_access_key_id=compatible_regions[model_id][i % len(compatible_regions[model_id])][0],
                                                        region_name=compatible_regions[model_id][i % len(compatible_regions[model_id])][2],
                                                        aws_secret_access_key=compatible_regions[model_id][i % len(compatible_regions[model_id])][1])
                    clients[model_id].append(client)
                except Exception as ex:
                    print(f"❌ {model_id}模型测试失败 ({item['region_name']}): {ex}")

        return clients

    def _data_generator(self, data_list):
        for data in data_list:
            yield data

    def build_prompt(self, row):
        item_name = row['item_name']
        bullet_point_list = []
        if "bullet_points" in row:
            bullet_points = row["bullet_points"]
        else:
            for i in range(1, 6):
                if pd.isna(row[f'bullet_point{i}']) or str(row[f'bullet_point{i}']).strip() == "":
                    continue
                bullet_point_list.append(row[f'bullet_point{i}'])
            bullet_points = "\n".join(bullet_point_list)
        
        # 处理NaN值
        if pd.isna(bullet_points):
            bullet_points = ""
        
        search_keyword = str(row['keywords'])
        pt = " ".join(row['product_type'].lower().split("_"))

        prompt = self.prompt_func(item_name, bullet_points, search_keyword, pt)

        return prompt

    def get_claude_response(self, client_idx, prompt):
        body = json.dumps({
            "max_tokens": 1024,
            "system": "",
            "messages": [{"role": "user", "content": [
                {
                    "type": "text",
                    "text": prompt
                }
            ]}],
            "anthropic_version": "bedrock-2023-05-31"
        })
        
        modelId = self.clients[client_idx][0]
        client = self.clients[client_idx][1]
        retry = 0
        max_retry = 20
        while retry < max_retry:
            try:
                response = client.invoke_model(body=body, modelId=modelId)
                response_body = json.loads(response.get('body').read())
                answer = response_body.get('content')
                answer = answer[0]['text']
                break
            except Exception as e:
                if retry >= 3:
                    modelId = "anthropic.claude-3-sonnet-20240229-v1:0"
                # else:
                #     modelId = "anthropic.claude-3-5-sonnet-20240620-v1:0"
                # print("error: ", e)
                # modelId = 'anthropic.claude-3-5-sonnet-20241022-v2:0'
                # modelId = "anthropic.claude-3-5-haiku-20241022-v1:0"
                time.sleep(1)
                retry += 1
        else:
            answer = 'no output'

        if answer != 'no output':
            answer = re.sub(r"\n", "", answer)
        return answer

    def extract_in_scope_result(self, response_text):
        """从响应中提取in_scope结果"""
        if response_text == 'no output' or response_text == 'unknown':
            return 'unknown'
        
        try:
            # 尝试提取JSON格式的结果
            json_match = re.search(r"\{.*?\}", response_text)
            if json_match:
                result_json = json.loads(json_match.group(0))
                return result_json.get("in_scope", "unknown").lower()
        except:
            pass
        
        # 如果JSON提取失败，尝试简单的文本匹配
        response_lower = response_text.lower()
        if 'in_scope": "no"' in response_lower or '"in_scope":"no"' in response_lower:
            return 'no'
        elif 'in_scope": "yes"' in response_lower or '"in_scope":"yes"' in response_lower:
            return 'yes'
        
        return 'unknown'

    def call_claude(self, client_idx, row):
        prompt = self.build_prompt(row)
        claude_response = self.get_claude_response(client_idx, prompt)
        
        return {"asin": row['asin'], "product_type": row['product_type'], "quality_label": row['quality_label'], "keywords": row['keywords'],  "claude_response": claude_response}

    def multi_thrad_run(self, df, output_file):
        data_gen = self._data_generator(df.to_dict(orient='records'))
        pbar = tqdm(range(df.shape[0]))
        max_clients = min(len(self.clients), df.shape[0])

        with open(output_file, "a+") as f:
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_clients) as executor:
                futures = {executor.submit(self.call_claude, i, next(data_gen)): i for i in range(max_clients)}
                # result_list = []
                while futures:     
                    done, _ = concurrent.futures.wait(futures, return_when=concurrent.futures.FIRST_COMPLETED, timeout=1)
                    for future in done:
                        client_id = futures.pop(future)
                        try:
                            result = future.result()
                            f.write(json.dumps(result) + '\n')
                            f.flush()  # 确保立即写入文件

                            try:
                                next_data = next(data_gen)
                                futures[executor.submit(self.call_claude, client_id, next_data)] = client_id
                            except StopIteration:
                                pass

                        except Exception as e:
                            print(e)
                            try:
                                next_data = next(data_gen)
                                futures[executor.submit(self.call_claude, client_id, next_data)] = client_id
                            except StopIteration:
                                pass

                        pbar.update(1)
