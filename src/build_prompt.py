def main_prompt(item_name, bullet_points, search_keyword, product_type) -> str:
    """
    Build prompt for analyzing search query and product matching with focus on negation detection
    
    Args:
        item_name: Product title
        bullet_points: Product description/bullet points
        search_keyword: User search query
        product_type: Product category
    
    Returns:
        Built prompt string in English
    """
    
    # Handle empty bullet points
    if not bullet_points or str(bullet_points).strip() == "" or str(bullet_points).lower() == "nan":
        bullet_points = "No detailed description available"
    
    prompt = f"""
You are a professional e-commerce search matching analyst. Your task is to analyze the matching degree between user search queries and Amazon recommended products, with special attention to negation words and their impact on search intent.

## Analysis Task

**User Search Query:** {search_keyword}

**Recommended Product Information:**
- Product Title: {item_name}
- Product Description: {bullet_points}
- Product Type: {product_type}

## Analysis Framework

Please conduct a comprehensive analysis following these steps:

### 1. Negation Detection
First, carefully examine the search query to identify any negation expressions, including:
- Direct negation words (no, not, without, except, minus, exclude, excluding, avoid, etc.)
- Negation phrases (don't want, do not want, other than, rather than, instead of, etc.)
- Exclusionary expressions (lacking, missing, absent, etc.)
Please note that the symbol "-" (dash) or minus symbol should not be treated as a negation in this context.

### 2. Search Intent Analysis
Based on your negation detection, determine:
- What features/functions the user WANTS (desired features)
- What features/functions the user explicitly DOES NOT WANT (excluded features)
- The overall search intent (positive search vs. negative search vs. mixed search)

### 3. Product Feature Analysis
Analyze the product title and description to identify:
- Core product functions and features
- Key product characteristics mentioned
- Any features that might conflict with user intent

### 4. Matching Assessment
Evaluate the overall matching degree by considering:
- Whether the product provides desired features
- Whether the product contains any excluded features
- The severity of any conflicts between user intent and product features

## Critical Evaluation Principles

- **Negation Priority**: If the product contains features that the user explicitly wants to exclude, it should be considered a mismatch regardless of other positive aspects
- **Context Understanding**: Consider the semantic meaning and context, not just keyword matching
- **Feature Hierarchy**: Focus on core functionalities rather than minor details
- **User Intent**: Prioritize what the user actually needs over what they don't specify

## Output Requirements

Please provide your analysis result in the following JSON format without any additional text:

{{
    "has_negative_keywords": true/false,
    "detected_negatives": ["list", "of", "detected", "negation", "words"],
    "excluded_features": ["list", "of", "features", "user", "wants", "to", "exclude"],
    "desired_features": ["list", "of", "features", "user", "wants"],
    "match_status": "match/mismatch/partial_match",
    "confidence_score": 0.0,
    "reason": "Simple explanation of your matching assessment, including key feature analysis and negation handling",
    "key_conflicts": ["list", "of", "main", "conflicts", "if", "any"]
}}

## Field Definitions

- **has_negative_keywords**: Whether the search query contains any form of negation. Please note that the symbol "-" (dash) or minus symbol should not be treated as a negation in this context.
- **detected_negatives**: All negation words/phrases you identified in the search query
- **excluded_features**: Features/functions the user explicitly wants to avoid
- **desired_features**: Features/functions the user is looking for
- **match_status**: 
  - "match": Product fully meets user search intent
  - "mismatch": Product conflicts with user search intent (especially when containing excluded features)
  - "partial_match": Product partially meets intent but has some misalignments
- **confidence_score**: Your confidence in the assessment (0.0-1.0, where 1.0 is completely certain)
- **reason**: Comprehensive explanation of your analysis and conclusion
- **key_conflicts**: Specific conflicts between user intent and product features (if any)

## Examples for Reference

**Example 1 - Negation Case:**
Search: "power strip without surge protector"
Product: "12-Outlet Surge Protector Power Strip"
Expected Analysis: Negation detected ("without"), user wants power strip but excludes surge protector, product contains excluded feature → mismatch

**Example 2 - Positive Case:**
Search: "wireless gaming mouse"
Product: "Logitech Wireless Gaming Mouse with RGB"
Expected Analysis: No negation, user wants wireless gaming mouse, product matches requirements → match

Now please analyze the given search query and product information:
"""

    return prompt


def technical_terms_prompt(item_name, bullet_points, search_keyword, product_type) -> str:
    """
    Build prompt for detecting technical terms in search keywords and analyzing product matching
    
    Args:
        item_name: Product title
        bullet_points: Product description/bullet points
        search_keyword: User search query
        product_type: Product category
    
    Returns:
        Built prompt string in English for technical terms analysis
    """
    
    # Handle empty bullet points
    if not bullet_points or str(bullet_points).strip() == "" or str(bullet_points).lower() == "nan":
        bullet_points = "No detailed description available"
    
    prompt = f"""
You are a professional e-commerce search analyst specializing in technical product terminology. Your task is to analyze user search queries for technical terms and evaluate whether recommended products can fully satisfy the technical requirements expressed in the search keywords.

## Analysis Task

**User Search Query:** {search_keyword}

**Recommended Product Information:**
- Product Title: {item_name}
- Product Description: {bullet_points}
- Product Type: {product_type}

## Analysis Framework

Please conduct a comprehensive analysis following these steps:

### 1. Technical Terms Detection
Identify all technical terms, specifications, and technical language in the search query, including:
- Technical specifications (USB-C, USB-B, 3.5mm, 802.11ac, Bluetooth 5.0, etc.)
- Interface types and connectors (HDMI, DisplayPort, Lightning, etc.)
- Technical standards and protocols (Wi-Fi 6, 4K, HDR, etc.)
- Model numbers and technical identifiers
- Technical adjectives and descriptors (wireless, wired, compatible, etc.)
- Measurement units and technical quantities (watts, volts, inches, etc.)

### 2. Technical Requirements Analysis
Based on the detected technical terms, determine:
- What specific technical features/capabilities the user requires
- What technical compatibility requirements exist
- What technical performance standards are expected
- Any technical constraints or limitations implied

### 3. Product Technical Capability Assessment
Analyze the product information to identify:
- Technical specifications and capabilities mentioned
- Interface types and compatibility features
- Technical standards supported
- Any technical limitations or missing capabilities

### 4. Technical Matching Evaluation
Evaluate whether the product can fulfill the technical requirements by considering:
- Does the product support all required technical specifications?
- Are there any technical compatibility issues?
- Does the product meet the technical performance requirements?
- Are there any technical gaps that would prevent full satisfaction of user needs?

## Critical Evaluation Principles

- **Technical Precision**: Technical terms must be interpreted precisely - "USB-C to USB-B" requires both specific connector types
- **Compatibility Focus**: Emphasize technical compatibility and interoperability requirements
- **Specification Completeness**: A product must meet ALL technical specifications mentioned in the search query
- **Technical Context**: Consider the technical use case and application context
- **Standard Compliance**: Verify adherence to relevant technical standards

## Output Requirements

Please provide your analysis result in the following JSON format without any additional text:

{{
    "has_technical_terms": true/false,
    "detected_technical_terms": ["list", "of", "technical", "terms", "found"],
    "technical_requirements": ["list", "of", "technical", "requirements", "derived"],
    "product_technical_specs": ["list", "of", "product", "technical", "specifications"],
    "technical_match_status": "full_match/partial_match/technical_mismatch",
    "confidence_score": 0.0,
    "reason": "Detailed explanation of technical matching assessment",
    "technical_gaps": ["list", "of", "technical", "requirements", "not", "met"],
    "compatibility_issues": ["list", "of", "compatibility", "problems", "if", "any"]
}}

## Field Definitions

- **has_technical_terms**: Whether the search query contains technical terminology
- **detected_technical_terms**: All technical terms/specifications identified in the search query
- **technical_requirements**: Technical capabilities/features the user needs based on the search terms
- **product_technical_specs**: Technical specifications and capabilities found in the product information
- **technical_match_status**: 
  - "full_match": Product fully meets all technical requirements
  - "partial_match": Product meets some but not all technical requirements
  - "technical_mismatch": Product fails to meet critical technical requirements
- **confidence_score**: Your confidence in the technical assessment (0.0-1.0)
- **reason**: Comprehensive explanation of your technical analysis and conclusion
- **technical_gaps**: Specific technical requirements that the product does not fulfill
- **compatibility_issues**: Technical compatibility problems between user needs and product capabilities

## Examples for Reference

**Example 1 - Technical Mismatch:**
Search: "USB-C to USB-B adapter"
Product: "USB-A to USB-C Cable"
Expected Analysis: Technical terms detected (USB-C, USB-B), user needs specific adapter type, product provides different connector combination → technical_mismatch

**Example 2 - Full Technical Match:**
Search: "Bluetooth 5.0 wireless earbuds"
Product: "Sony WF-1000XM4 Wireless Earbuds with Bluetooth 5.2"
Expected Analysis: Technical terms detected (Bluetooth 5.0, wireless), product exceeds minimum Bluetooth requirement → full_match

**Example 3 - Partial Technical Match:**
Search: "4K HDR monitor with USB-C"
Product: "4K Monitor with HDMI and DisplayPort"
Expected Analysis: Technical terms detected (4K, HDR, USB-C), product has 4K but lacks USB-C connectivity → partial_match

Now please analyze the given search query and product information:
"""

    return prompt


# Backward compatibility function
def build_search_match_prompt(item_name, bullet_points, search_keyword, product_type):
    """Backward compatibility function"""
    return main_prompt(item_name, bullet_points, search_keyword, product_type)
