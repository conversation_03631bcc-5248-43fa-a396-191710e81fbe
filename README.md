# UDE Analysis 项目使用说明

## 项目概述

UDE Analysis 是一个专门用于分析亚马逊用户搜索词与推荐商品匹配度的工具，特别关注否定词（如 "without", "no", "not" 等）对搜索意图的影响。该系统使用**纯英文prompt**并完全依赖**大模型智能判断**否定词，无需预定义规则。

## 核心功能

### 1. 智能否定词检测 
系统通过**大模型语言理解能力**自动识别搜索词中的各种否定表达：
- 直接否定词：no, not, without, except, minus, exclude, excluding, avoiding, avoid
- 否定前缀：non-, un-, dis-, etc.
- 否定短语：don't want, do not want, other than, rather than, instead of
- 排除性表达：free of, free from, devoid of, lacking, missing, absent
- 替代性表达：replacement for, alternative to, substitute for
- **复杂语义否定**：LLM能理解更微妙的否定表达和上下文

### 2. 搜索意图分析
- **正向搜索**：用户明确想要的功能和特征
- **否定搜索**：用户明确想要排除的功能和特征
- **混合搜索**：同时包含期望和排除的特征

### 3. 匹配度评估
系统输出详细的匹配度分析，包括：
- 是否检测到否定词（LLM自动判断）
- 用户期望的特征（LLM自动提取）
- 用户明确排除的特征（LLM智能识别）
- 匹配状态（完全匹配/不匹配/部分匹配）
- 置信度分数（LLM自评估）
- 详细分析理由（英文输出）
- 主要冲突点（具体冲突描述）

## 输入数据格式

系统需要以下字段的数据：
```
- product_type: 产品类型（如 "INPUT_MOUSE"）
- quality_label: 质量标签（如 "irrelevant"）
- keywords: 用户搜索词（如 "Power strip without surge protector"）
- asin: 亚马逊商品ID
- item_name: 商品标题
- bullet_points: 商品要点描述
- product_description: 商品详细描述（可选）
```

## 输出数据格式

系统输出包含以下字段的JSON格式结果（全英文）：
```json
{
    "has_negative_keywords": true/false,
    "detected_negatives": ["without", "no"],
    "excluded_features": ["surge protector", "noise"],
    "desired_features": ["power strip", "wireless"],
    "match_status": "match/mismatch/partial_match",
    "confidence_score": 0.85,
    "reason": "Detailed analysis in English...",
    "key_conflicts": ["Product contains surge protector which user wants to exclude"]
}
```

## 使用示例

### 示例1：否定词搜索
**搜索词**: "Power strip without surge protector"
**推荐商品**: "Belkin 12-Outlet Surge Protector Power Strip"

**分析结果**:
- ✅ 检测到否定词: "without"
- ✅ 用户想要: "power strip"
- ✅ 用户不想要: "surge protector"
- ❌ 匹配状态: "mismatch"（商品明确包含被排除的功能）

### 示例2：正向搜索
**搜索词**: "wireless gaming mouse"
**推荐商品**: "Logitech G502 HERO Gaming Mouse (Wired)"

**分析结果**:
- ✅ 无否定词
- ✅ 用户想要: "wireless gaming mouse"
- ❌ 匹配状态: "mismatch"（商品是有线的，不符合无线需求）

## 运行方法

### 1. 基本运行
```bash
cd /path/to/project
python ude_analysis/main.py
```

### 2. 数据准备
确保输入数据文件位于: `ude_analysis/data/asin_search_data.xlsx`

### 3. 结果输出
- 处理日志: `ude_analysis/data/log.json`
- 最终结果: `ude_analysis/data/output_data.xlsx`

## 配置说明

### LLM模型配置
系统支持多种Claude模型：
- Claude 3.7 Sonnet (默认推荐)
- Claude 3.5 Sonnet V2
- Claude 3.5 Sonnet
- Claude 3.5 Haiku

### AWS配置
需要在DynamoDB中配置Claude账户信息，表名: `claude_account`

## 关键特性

### 1. 大模型智能分析
- **无规则依赖**：完全依赖LLM语言理解能力，无需预定义否定词规则
- **语义深度理解**：识别复杂的否定表达和微妙的语言细节
- **上下文感知**：理解否定词在不同语境中的具体含义
- **自适应学习**：能处理新出现的否定表达方式

### 2. 纯英文界面
- **国际化标准**：使用纯英文prompt和输出
- **专业术语**：采用电商和产品分析专业词汇
- **清晰结构**：结构化的英文分析框架
- **一致性输出**：标准化的JSON格式英文结果

### 3. 智能特征提取
- **自动识别期望特征**：LLM自动提取用户想要的功能
- **精准排除特征**：智能识别用户明确不想要的功能
- **语义匹配评估**：深度理解产品特征与用户意图的匹配度
- **冲突点定位**：准确识别和描述具体的不匹配原因

## 故障排除

### 常见问题

1. **模型调用失败**
   - 检查AWS凭证配置
   - 确认DynamoDB表权限
   - 验证模型ID有效性

2. **否定词识别不准确**
   - 检查搜索词语法结构
   - 查看否定词模式匹配规则
   - 考虑添加自定义否定模式

3. **输出解析错误**
   - 检查JSON格式完整性
   - 验证字段名称一致性
   - 查看模型响应原始内容

### 调试建议

1. 使用小数据集进行测试
2. 检查中间处理结果
3. 验证prompt格式正确性
4. 监控API调用成功率

## 扩展功能

### 自定义否定词模式
可以在 `build_prompt.py` 中的 `negative_patterns` 列表添加新的否定词模式：

```python
negative_patterns.append(r'\bcustom_pattern\b')
```

### 新增分析维度
可以扩展输出JSON格式，添加更多分析维度：
- 品牌匹配度
- 价格合理性
- 功能完整性

## 技术架构

- **前端处理**: Excel数据读取和预处理
- **核心分析**: 基于正则表达式的否定词检测
- **LLM推理**: Claude模型进行语义分析
- **后端存储**: JSON格式的结果存储
- **结果导出**: Excel格式的最终报告

## 性能优化

- 多线程并发处理
- 智能重试机制
- 缓存已处理结果
- 批量数据处理

## 版本更新

### v3.0 (当前版本) - LLM智能分析版
- ✅ **移除规则依赖**：完全依赖LLM智能判断否定词，无需预定义规则
- ✅ **纯英文界面**：prompt和输出全部使用英文，国际化标准
- ✅ **智能特征提取**：新增desired_features字段，LLM自动提取用户期望特征
- ✅ **语义深度理解**：LLM能理解更复杂和微妙的否定表达
- ✅ **专业分析框架**：结构化的英文分析步骤和评估原则

### v2.0 (历史版本) - 规则增强版
- 重新设计prompt专注于搜索匹配分析
- 基于正则表达式的否定词检测
- 中文prompt和输出
- 修复原有bug和错误引用

### v1.0 (历史版本) - 基础版
- 基础商品分类功能
- 简单的prompt模板
- 基本的LLM调用框架 