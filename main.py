import re
import os
import sys
import boto3
import pandas as pd
from tqdm import tqdm
import json
import concurrent.futures
import threading
import time
import argparse
BASE_DIR = os.path.dirname(os.path.abspath("{}/../".format(__file__)))
sys.path.append(BASE_DIR)

from ude_analysis.src.call_llm import LLM
from ude_analysis.src.build_prompt import main_prompt, technical_terms_prompt


def result_extract(row, prompt_type="negation"):
    try:
        claude_output = row["claude_response"]
        claude_output = re.search(r"\{.*?\}", str(claude_output), re.DOTALL).group(0)
        result_json = json.loads(claude_output)
        
        product_type = row['product_type']
        quality_label = row['quality_label']
        keywords = row['keywords']
        
        if prompt_type == "negation":
            # 否定词检测prompt的输出格式
            has_negative_keywords = result_json.get("has_negative_keywords", False)
            detected_negatives = result_json.get("detected_negatives", [])
            excluded_features = result_json.get("excluded_features", [])
            desired_features = result_json.get("desired_features", [])
            match_status = result_json.get("match_status", "unknown")
            confidence_score = result_json.get("confidence_score", 0.0)
            reason = result_json.get("reason", "")
            key_conflicts = result_json.get("key_conflicts", [])
            
            return (has_negative_keywords, detected_negatives, excluded_features, 
                    desired_features, match_status, confidence_score, reason, key_conflicts, product_type, quality_label, keywords)
        
        elif prompt_type == "technical":
            # 技术词汇检测prompt的输出格式
            has_technical_terms = result_json.get("has_technical_terms", False)
            detected_technical_terms = result_json.get("detected_technical_terms", [])
            technical_requirements = result_json.get("technical_requirements", [])
            product_technical_specs = result_json.get("product_technical_specs", [])
            technical_match_status = result_json.get("technical_match_status", "unknown")
            confidence_score = result_json.get("confidence_score", 0.0)
            reason = result_json.get("reason", "")
            technical_gaps = result_json.get("technical_gaps", [])
            compatibility_issues = result_json.get("compatibility_issues", [])
            
            return (has_technical_terms, detected_technical_terms, technical_requirements, 
                    product_technical_specs, technical_match_status, confidence_score, reason, technical_gaps, compatibility_issues, product_type, quality_label, keywords)
                
    except Exception as ex:
        if prompt_type == "negation":
            return (False, [], [], [], "unknown", 0.0, f"解析错误: {str(ex)}", [], product_type, quality_label, keywords)
        else:
            return (False, [], [], [], "unknown", 0.0, f"解析错误: {str(ex)}", [], [], product_type, quality_label, keywords)


def main(prompt_type="negation"): 
    # 设置输出文件路径
    output_file = f"{BASE_DIR}/ude_analysis/data/log_{prompt_type}.json"
    chunk_data = pd.read_csv(f"{BASE_DIR}/ude_analysis/data/new_asin_search_data_final.csv")
    
    # 运行LLM处理
    llm.multi_thrad_run(chunk_data, output_file)
    
    # find the asins without resutls and re-run the script
    with open(output_file, "r") as f:
        claude_response = f.readlines()
        claude_response = [json.loads(line) for line in claude_response]

    claude_response_df = pd.DataFrame(claude_response)
    claude_response_df = claude_response_df[claude_response_df["claude_response"] != "no output"]

    # 解析输出格式
    extracted_results = claude_response_df.apply(lambda row: result_extract(row, prompt_type), axis=1)
    
    if prompt_type == "negation":
        (claude_response_df["has_negative_keywords"], 
         claude_response_df["detected_negatives"],
         claude_response_df["excluded_features"],
         claude_response_df["desired_features"], 
         claude_response_df["match_status"], 
         claude_response_df["confidence_score"], 
         claude_response_df["reason"],
         claude_response_df["key_conflicts"],
         claude_response_df["product_type"],
         claude_response_df["quality_label"],
         claude_response_df["keywords"]) = zip(*extracted_results)
    else:  # technical
        (claude_response_df["has_technical_terms"], 
         claude_response_df["detected_technical_terms"],
         claude_response_df["technical_requirements"],
         claude_response_df["product_technical_specs"], 
         claude_response_df["technical_match_status"], 
         claude_response_df["confidence_score"], 
         claude_response_df["reason"],
         claude_response_df["technical_gaps"],
         claude_response_df["compatibility_issues"],
         claude_response_df["product_type"],
         claude_response_df["quality_label"],
         claude_response_df["keywords"]) = zip(*extracted_results)

    claude_response_df.to_excel(f"{BASE_DIR}/ude_analysis/data/output_data_{prompt_type}.xlsx", index=False)


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Run UDE analysis with different prompt types')
    parser.add_argument('--prompt_type', '-p', choices=['negation', 'technical'], 
                      default='technical',
                      help='Type of prompt to use: negation (default) or technical')
    
    args = parser.parse_args()
    prompt_type = args.prompt_type
    
    # 输出当前使用的prompt类型
    print(f"使用 {prompt_type} prompt 类型进行分析...")
    
    # 设置prompt函数
    prompt_func = technical_terms_prompt if prompt_type == "technical" else main_prompt
    
    llm = LLM(model_name="Claude 3.7 Sonnet", prompt_func=prompt_func)

    # read existing claude response
    existing_response_file = f"{BASE_DIR}/ude_analysis/data/log_{prompt_type}.json"
    if os.path.exists(existing_response_file):
        with open(existing_response_file, "r") as f:
            existing_claude_response = f.readlines()
            existing_claude_response = [json.loads(line) for line in existing_claude_response]
            existing_claude_response = {item["asin"]: item["claude_response"] for item in existing_claude_response}
    else:
        existing_claude_response = {}

    llm.existing_claude_response = existing_claude_response
    main(prompt_type)
