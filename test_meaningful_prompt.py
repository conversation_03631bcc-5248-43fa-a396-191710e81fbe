#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for meaningful search prompt
"""

import os
import sys
BASE_DIR = os.path.dirname(os.path.abspath("{}/../".format(__file__)))
sys.path.append(BASE_DIR)

from ude_analysis.src.build_prompt import meaningful_search_prompt

def test_meaningful_search_prompt():
    """Test meaningful search prompt function"""
    
    # 测试用例
    test_cases = [
        # Meaningful searches
        "wireless bluetooth headphones",
        "iphone 15 case",
        "gaming mouse",
        "usb-c cable",
        "coffee maker",
        
        # Meaningless searches
        "asdfghjkl",
        "test123",
        "qwerty",
        "123456",
        "random text here",
        
        # Edge cases
        "a",  # Single letter
        "phone",  # Short but meaningful
        "test",  # Possibly a test
        "hello world",  # Non-shopping related
    ]
    
    print("Testing meaningful_search_prompt function...")
    print("=" * 60)

    for i, search_keyword in enumerate(test_cases, 1):
        print("\nTest case {}: '{}'".format(i, search_keyword))
        print("-" * 40)

        try:
            prompt = meaningful_search_prompt(search_keyword)
            print("✅ Prompt generated successfully")

            # Show first and last few lines of prompt
            lines = prompt.split('\n')
            print("Prompt total lines: {}".format(len(lines)))
            print("First 5 lines:")
            for line in lines[:5]:
                print("  {}".format(line))
            print("...")
            print("Last 5 lines:")
            for line in lines[-5:]:
                print("  {}".format(line))

        except Exception as e:
            print("❌ Prompt generation failed: {}".format(e))

    print("\n" + "=" * 60)
    print("Testing completed!")

if __name__ == "__main__":
    test_meaningful_search_prompt()
