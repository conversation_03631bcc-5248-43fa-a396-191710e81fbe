#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDE Analysis v3.0 Demo Script
Demonstrates the new LLM-powered intelligent negation detection and English analysis
"""

import sys
import os
BASE_DIR = os.path.dirname(os.path.abspath("{}/../".format(__file__)))
sys.path.append(BASE_DIR)

from ude_analysis.src.build_prompt import main_prompt

def demo_english_prompt():
    """Demonstrate the new English prompt with LLM intelligent analysis"""
    
    print("🚀 UDE Analysis v3.0 Demo - LLM Intelligent Negation Detection")
    print("=" * 70)
    
    # Demo cases showcasing different types of negation
    demo_cases = [
        {
            "name": "Direct Negation (without)",
            "search": "power strip without surge protector",
            "title": "Belkin 12-Outlet Surge Protector Power Strip",
            "description": "Advanced surge protector with 4,320 joules protection\nEMI/RFI noise filtering\n8-foot heavy duty extension cord"
        },
        {
            "name": "Prefix Negation (non-)",
            "search": "non-slip yoga mat",
            "title": "Basic Yoga Mat - Standard Surface",
            "description": "Lightweight yoga mat\nPortable design\nEasy to clean\nStandard surface texture"
        },
        {
            "name": "Alternative Expression (instead of)",
            "search": "wireless mouse instead of wired",
            "title": "Logitech Wired Gaming Mouse",
            "description": "High-performance wired gaming mouse\nUSB cable connection\nErgonomic design\nPrecision sensor"
        },
        {
            "name": "Positive Search (no negation)",
            "search": "bluetooth wireless headphones",
            "title": "Sony WH-1000XM4 Wireless Bluetooth Headphones",
            "description": "Industry leading noise canceling\n30-hour battery life\nPremium sound quality\nWireless Bluetooth connectivity"
        }
    ]
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n📋 Demo Case {i}: {case['name']}")
        print("-" * 50)
        print(f"🔍 Search Query: '{case['search']}'")
        print(f"📦 Product: {case['title'][:60]}...")
        
        # Generate the prompt
        prompt = main_prompt(
            case["title"],
            case["description"],
            case["search"],
            "demo_product"
        )
        
        print(f"✅ Generated English prompt: {len(prompt)} characters")
        print("🤖 Key features of the prompt:")
        print("   • LLM will autonomously detect negation expressions")
        print("   • No pre-defined rules or regex patterns")
        print("   • Comprehensive analysis framework in English")
        print("   • Structured JSON output with detailed reasoning")
        
        # Show a snippet of the prompt
        print(f"\n📄 Prompt Preview:")
        lines = prompt.split('\n')[:8]
        for line in lines:
            print(f"   {line[:80]}{'...' if len(line) > 80 else ''}")
        print("   ...")
        
        print(f"\n🎯 Expected LLM Analysis:")
        if "without" in case['search'] or "non-" in case['search'] or "instead of" in case['search']:
            print("   • has_negative_keywords: true")
            print("   • Will detect and extract negation expressions")
            print("   • Will identify excluded vs desired features")
            print("   • Likely result: mismatch (due to negation conflict)")
        else:
            print("   • has_negative_keywords: false")
            print("   • Will focus on positive feature matching")
            print("   • Will extract desired features only")
            print("   • Likely result: match (if features align)")
        
        if i < len(demo_cases):
            input("\n⏸️  Press Enter to continue to next demo...")
    
    print("\n" + "=" * 70)
    print("🎉 Demo Complete!")
    print("\n🔧 To run actual analysis:")
    print("   python ude_analysis/main.py")
    print("\n📚 For more information:")
    print("   cat ude_analysis/README.md")
    print("\n✨ Key advantages of v3.0:")
    print("   • No maintenance of regex patterns")
    print("   • Handles complex and subtle negations")
    print("   • Professional English output")
    print("   • LLM contextual understanding")

if __name__ == "__main__":
    demo_english_prompt() 