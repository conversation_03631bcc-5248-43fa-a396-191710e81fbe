import re
import os
import sys
import boto3
import pandas as pd
from tqdm import tqdm
import json
import concurrent.futures
import threading
import time
import argparse
BASE_DIR = os.path.dirname(os.path.abspath("{}/".format(__file__)))
sys.path.append(BASE_DIR)

from src.call_llm import LLM
from src.build_prompt import main_prompt, technical_terms_prompt, meaningful_search_prompt


def result_extract(row, prompt_type="negation"):
    """从Claude响应中提取结果"""
    try:
        claude_output = row["claude_response"]
        claude_output = re.search(r"\{.*?\}", str(claude_output), re.DOTALL).group(0)
        result_json = json.loads(claude_output)
        
        product_type = row['product_type']
        quality_label = row['quality_label']
        keywords = row['keywords']
        
        if prompt_type == "meaningful":
            # 有意义搜索检测prompt的输出格式
            is_meaningful = result_json.get("is_meaningful", False)
            confidence_score = result_json.get("confidence_score", 0.0)
            search_intent_type = result_json.get("search_intent_type", "unclear")
            reason = result_json.get("reason", "")
            detected_issues = result_json.get("detected_issues", [])
            potential_products = result_json.get("potential_products", [])
            
            return (is_meaningful, confidence_score, search_intent_type, reason, 
                    detected_issues, potential_products, product_type, quality_label, keywords)
        
        elif prompt_type == "negation":
            # 否定词检测prompt的输出格式
            has_negative_keywords = result_json.get("has_negative_keywords", False)
            detected_negatives = result_json.get("detected_negatives", [])
            excluded_features = result_json.get("excluded_features", [])
            desired_features = result_json.get("desired_features", [])
            match_status = result_json.get("match_status", "unknown")
            confidence_score = result_json.get("confidence_score", 0.0)
            reason = result_json.get("reason", "")
            key_conflicts = result_json.get("key_conflicts", [])
            
            return (has_negative_keywords, detected_negatives, excluded_features, 
                    desired_features, match_status, confidence_score, reason, key_conflicts, product_type, quality_label, keywords)
        
        elif prompt_type == "technical":
            # 技术词汇检测prompt的输出格式
            has_technical_terms = result_json.get("has_technical_terms", False)
            detected_technical_terms = result_json.get("detected_technical_terms", [])
            technical_requirements = result_json.get("technical_requirements", [])
            product_technical_specs = result_json.get("product_technical_specs", [])
            technical_match_status = result_json.get("technical_match_status", "unknown")
            confidence_score = result_json.get("confidence_score", 0.0)
            reason = result_json.get("reason", "")
            technical_gaps = result_json.get("technical_gaps", [])
            compatibility_issues = result_json.get("compatibility_issues", [])
            
            return (has_technical_terms, detected_technical_terms, technical_requirements, 
                    product_technical_specs, technical_match_status, confidence_score, reason, technical_gaps, compatibility_issues, product_type, quality_label, keywords)
                
    except Exception as ex:
        if prompt_type == "meaningful":
            return (False, 0.0, "unclear", f"解析错误: {str(ex)}", [], [], product_type, quality_label, keywords)
        elif prompt_type == "negation":
            return (False, [], [], [], "unknown", 0.0, f"解析错误: {str(ex)}", [], product_type, quality_label, keywords)
        else:
            return (False, [], [], [], "unknown", 0.0, f"解析错误: {str(ex)}", [], [], product_type, quality_label, keywords)


def run_meaningful_analysis(data_file):
    """第一阶段：运行有意义搜索检测"""
    print("第一阶段：检测搜索词是否有意义...")
    
    # 设置输出文件路径
    output_file = f"{BASE_DIR}/ude_analysis/data/log_meaningful.json"
    chunk_data = pd.read_csv(data_file)
    
    # 设置LLM
    llm = LLM(model_name="Claude 3.7 Sonnet", prompt_func=meaningful_search_prompt)
    
    # 运行LLM处理
    llm.multi_thrad_run(chunk_data, output_file)
    
    # 读取并解析结果
    with open(output_file, "r") as f:
        claude_response = f.readlines()
        claude_response = [json.loads(line) for line in claude_response]

    claude_response_df = pd.DataFrame(claude_response)
    claude_response_df = claude_response_df[claude_response_df["claude_response"] != "no output"]

    # 解析输出格式
    extracted_results = claude_response_df.apply(lambda row: result_extract(row, "meaningful"), axis=1)
    
    (claude_response_df["is_meaningful"], 
     claude_response_df["confidence_score"],
     claude_response_df["search_intent_type"],
     claude_response_df["reason"], 
     claude_response_df["detected_issues"], 
     claude_response_df["potential_products"],
     claude_response_df["product_type"],
     claude_response_df["quality_label"],
     claude_response_df["keywords"]) = zip(*extracted_results)

    # 保存第一阶段结果
    claude_response_df.to_excel(f"{BASE_DIR}/ude_analysis/data/output_data_meaningful.xlsx", index=False)
    
    # 筛选出有意义的搜索词
    meaningful_searches = claude_response_df[claude_response_df["is_meaningful"] == True]
    print(f"总共 {len(claude_response_df)} 个搜索词，其中 {len(meaningful_searches)} 个被识别为有意义的搜索词")
    
    return meaningful_searches


def run_second_stage_analysis(meaningful_data, analysis_type="negation"):
    """第二阶段：对有意义的搜索词运行negation或technical分析"""
    print(f"第二阶段：对有意义的搜索词运行 {analysis_type} 分析...")
    
    if len(meaningful_data) == 0:
        print("没有有意义的搜索词需要进一步分析")
        return
    
    # 设置输出文件路径
    output_file = f"{BASE_DIR}/ude_analysis/data/log_{analysis_type}_filtered.json"
    
    # 设置prompt函数
    if analysis_type == "technical":
        prompt_func = technical_terms_prompt
    else:  # negation
        prompt_func = main_prompt
    
    # 设置LLM
    llm = LLM(model_name="Claude 3.7 Sonnet", prompt_func=prompt_func)
    
    # 准备数据 - 需要原始数据格式
    original_data = pd.read_csv(f"{BASE_DIR}/ude_analysis/data/new_asin_search_data_final.csv")
    
    # 根据asin筛选出有意义搜索词对应的原始数据
    filtered_data = original_data[original_data['asin'].isin(meaningful_data['asin'])]
    
    print(f"筛选出 {len(filtered_data)} 条数据进行 {analysis_type} 分析")
    
    # 运行LLM处理
    llm.multi_thrad_run(filtered_data, output_file)
    
    # 读取并解析结果
    with open(output_file, "r") as f:
        claude_response = f.readlines()
        claude_response = [json.loads(line) for line in claude_response]

    claude_response_df = pd.DataFrame(claude_response)
    claude_response_df = claude_response_df[claude_response_df["claude_response"] != "no output"]

    # 解析输出格式
    extracted_results = claude_response_df.apply(lambda row: result_extract(row, analysis_type), axis=1)
    
    if analysis_type == "negation":
        (claude_response_df["has_negative_keywords"], 
         claude_response_df["detected_negatives"],
         claude_response_df["excluded_features"],
         claude_response_df["desired_features"], 
         claude_response_df["match_status"], 
         claude_response_df["confidence_score"], 
         claude_response_df["reason"],
         claude_response_df["key_conflicts"],
         claude_response_df["product_type"],
         claude_response_df["quality_label"],
         claude_response_df["keywords"]) = zip(*extracted_results)
    else:  # technical
        (claude_response_df["has_technical_terms"], 
         claude_response_df["detected_technical_terms"],
         claude_response_df["technical_requirements"],
         claude_response_df["product_technical_specs"], 
         claude_response_df["technical_match_status"], 
         claude_response_df["confidence_score"], 
         claude_response_df["reason"],
         claude_response_df["technical_gaps"],
         claude_response_df["compatibility_issues"],
         claude_response_df["product_type"],
         claude_response_df["quality_label"],
         claude_response_df["keywords"]) = zip(*extracted_results)

    # 保存第二阶段结果
    claude_response_df.to_excel(f"{BASE_DIR}/ude_analysis/data/output_data_{analysis_type}_filtered.xlsx", index=False)
    
    print(f"{analysis_type} 分析完成，结果已保存")


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Run two-stage UDE analysis: meaningful search detection + negation/technical analysis')
    parser.add_argument('--second_stage', '-s', choices=['negation', 'technical'], 
                      default='negation',
                      help='Type of second stage analysis: negation (default) or technical')
    parser.add_argument('--data_file', '-d', 
                      default=f"{BASE_DIR}/ude_analysis/data/new_asin_search_data_final.csv",
                      help='Input data file path')
    
    args = parser.parse_args()
    
    print("开始两阶段分析流程...")
    print(f"数据文件: {args.data_file}")
    print(f"第二阶段分析类型: {args.second_stage}")
    
    # 第一阶段：检测搜索词是否有意义
    meaningful_data = run_meaningful_analysis(args.data_file)
    
    # 第二阶段：对有意义的搜索词进行进一步分析
    run_second_stage_analysis(meaningful_data, args.second_stage)
    
    print("两阶段分析流程完成！")


if __name__ == "__main__":
    main()
